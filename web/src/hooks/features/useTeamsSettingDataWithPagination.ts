import * as React from 'react';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { 
  IUserChatItem, 
  FetchUserChatsAndChannelsPaginated 
} from '../accessors/useUserChatsAndChannelsAccessor';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';
import useTeamsChatsRepositoryAccessor from '../accessors/useTeamsChatsRepositoryAccessor';
import useRemoteTeamsChatsFeature from './useRemoteTeamsChatsFeature';
import useUserChatsPagination from './useUserChatsPagination';
import { ITeamsChatsItem, DbProvider } from '../../types/IGeraniumAttaneDB';

export interface UseTeamsSettingDataWithPaginationProps {
  fetchUserChatsAndChannelsPaginated?: FetchUserChatsAndChannelsPaginated;
  getTeamsChatsApi?: UseTeamsChatsApiReturnType['getTeamsChatsApi'];
  postTeamsChatsApi?: UseTeamsChatsApiReturnType['postTeamsChatsApi'];
  deleteTeamsChatsApi?: UseTeamsChatsApiReturnType['deleteTeamsChatsApi'];
  isModalOpen: boolean;
  openDB?: DbProvider;
  eventReporter: EventReporter;
  pageSize?: number;
}

export interface UseTeamsSettingDataWithPaginationReturnType {
  // データ状態
  allChatItems: IUserChatItem[];
  savedItems: Set<string>;
  isLoadingSavedItems: boolean;
  // ページネーション状態
  hasMore: boolean;
  isLoadingMore: boolean;
  // データ操作関数
  loadSavedItems: () => Promise<void>;
  saveSelectedItems: (selectedItems: Set<string>, allChatItems: IUserChatItem[]) => Promise<void>;
  loadMoreChats: () => Promise<void>;
  resetChats: () => void;
  // 状態更新関数
  setAllChatItems: React.Dispatch<React.SetStateAction<IUserChatItem[]>>;
  setSavedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
}

/**
 * Teams設定のデータ取得・保存ロジックを管理するカスタムフック（ページネーション対応）
 */
const useTeamsSettingDataWithPagination = (
  props: UseTeamsSettingDataWithPaginationProps
): UseTeamsSettingDataWithPaginationReturnType => {
  const {
    fetchUserChatsAndChannelsPaginated,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen,
    openDB,
    eventReporter,
    pageSize = 20,
  } = props;

  // リポジトリアクセサーとリモート機能を初期化
  const repositoryReturn = useTeamsChatsRepositoryAccessor(openDB);
  const apiReturn = { getTeamsChatsApi, postTeamsChatsApi, deleteTeamsChatsApi };
  const remoteFeature = useRemoteTeamsChatsFeature(repositoryReturn, apiReturn, eventReporter);

  const { addRemoteTeamsChats, deleteRemoteTeamsChats } = remoteFeature;

  // ページネーション機能を使用
  const {
    chatItems: allChatItems,
    hasMore,
    isLoading: isLoadingMore,
    error: paginationError,
    loadMore,
    reset: resetChats,
    loadInitial,
  } = useUserChatsPagination(fetchUserChatsAndChannelsPaginated, pageSize);

  // 保存済みアイテムの状態管理
  const [savedItems, setSavedItems] = React.useState<Set<string>>(new Set());
  const [isLoadingSavedItems, setIsLoadingSavedItems] = React.useState(false);

  // 保存済みアイテムを取得する関数
  const loadSavedItems = React.useCallback(async () => {
    if (!getTeamsChatsApi) return;

    setIsLoadingSavedItems(true);
    try {
      const savedTeamsChats = await getTeamsChatsApi();
      const savedItemIds = new Set(
        savedTeamsChats
          .map((item) => item.chatId || item.channelId)
          .filter((id): id is string => Boolean(id)),
      );
      setSavedItems(savedItemIds);
    } catch (loadError) {
      throw new Error(`保存済みアイテムの取得に失敗しました: ${loadError}`);
    } finally {
      setIsLoadingSavedItems(false);
    }
  }, [getTeamsChatsApi]);

  // 選択されたアイテムを保存する関数
  const saveSelectedItems = React.useCallback(async (
    selectedItems: Set<string>,
    currentAllChatItems: IUserChatItem[],
  ) => {
    if (!addRemoteTeamsChats || !deleteRemoteTeamsChats) {
      throw new Error('リモート機能が利用できません');
    }

    // 削除対象のアイテムを特定（保存済みだが選択されていないアイテム）
    const itemsToDeleteNow = Array.from(savedItems).filter((id) => !selectedItems.has(id));

    // 1. 削除処理を先に実行
    if (itemsToDeleteNow.length > 0) {
      // 削除対象のTeamsChatsアイテムを作成
      const itemsToDelete = itemsToDeleteNow.map((chatId) => {
        const originalItem = currentAllChatItems.find((item) => item.id === chatId);
        if (!originalItem) {
          throw new Error(`削除対象アイテムが見つかりません: ${chatId}`);
        }

        return {
          id: originalItem.id,
          name: originalItem.name,
          type: originalItem.type,
          chatType: originalItem.chatType,
          teamId: originalItem.teamId,
          countId: 0, // 削除時は不要
        } as ITeamsChatsItem;
      });

      // キューを使用して削除
      await Promise.all(
        itemsToDelete.map(async (item) => {
          try {
            await deleteRemoteTeamsChats(item);
          } catch (deleteError) {
            throw new Error(`Delete queue failed for item ${item.id}: ${deleteError}`);
          }
        }),
      );
    }

    // 2. 選択されたアイテムをすべてUpsert（新規追加または更新）
    if (selectedItems.size > 0) {
      // 選択されたアイテムを取得
      const selectedChatItems = currentAllChatItems.filter((item) => selectedItems.has(item.id));

      // TeamsChatsアイテムに変換（countIdは順番に割り当て）
      const teamsChatsItems = selectedChatItems.map((item, index) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        chatType: item.chatType,
        teamId: item.teamId,
        countId: index + 1,
      } as ITeamsChatsItem));

      // キューを使用して追加
      await Promise.all(
        teamsChatsItems.map(async (item) => {
          try {
            await addRemoteTeamsChats(item);
          } catch (apiError) {
            throw new Error(`Add queue failed for item ${item.countId}: ${apiError}`);
          }
        }),
      );
    }

    // 保存済みアイテムを更新
    setSavedItems(new Set(selectedItems));
  }, [addRemoteTeamsChats, deleteRemoteTeamsChats, savedItems]);

  // 次のページを読み込む関数
  const loadMoreChats = React.useCallback(async () => {
    if (paginationError) {
      throw new Error(`ページネーションエラー: ${paginationError}`);
    }
    await loadMore();
  }, [loadMore, paginationError]);

  // データ取得のEffect
  React.useEffect(() => {
    if (isModalOpen && fetchUserChatsAndChannelsPaginated) {
      // 並行処理
      Promise.all([
        // 初回チャットデータ取得
        loadInitial(),
        // 保存済みアイテム取得
        loadSavedItems(),
      ])
        .catch((err) => {
          // エラーを再スローして上位でハンドリングできるようにする
          throw new Error(`データ取得エラー: ${err}`);
        });
    }
  }, [isModalOpen, fetchUserChatsAndChannelsPaginated, loadInitial, loadSavedItems]);

  // allChatItemsの状態更新関数（互換性のため）
  const setAllChatItems = React.useCallback((
    value: React.SetStateAction<IUserChatItem[]>
  ) => {
    // ページネーション機能では直接状態を更新できないため、
    // この関数は互換性のためのダミー実装
    console.warn('setAllChatItems is not supported in pagination mode');
  }, []);

  return {
    // データ状態
    allChatItems,
    savedItems,
    isLoadingSavedItems,

    // ページネーション状態
    hasMore,
    isLoadingMore,

    // データ操作関数
    loadSavedItems,
    saveSelectedItems,
    loadMoreChats,
    resetChats,

    // 状態更新関数
    setAllChatItems,
    setSavedItems,
  };
};

export default useTeamsSettingDataWithPagination;
