import React from 'react';
import { IUserChatItem, IPaginatedResult } from '../accessors/useUserChatsAndChannelsAccessor';

export interface UseUserChatsPaginationReturnType {
  // 現在表示中のチャットアイテム
  chatItems: IUserChatItem[];
  // 次のページがあるかどうか
  hasMore: boolean;
  // ローディング状態
  isLoading: boolean;
  // エラー状態
  error: string | null;
  // 次のページを読み込む関数
  loadMore: () => Promise<void>;
  // データをリセットする関数
  reset: () => void;
  // 初回データを読み込む関数
  loadInitial: () => Promise<void>;
}

/**
 * ユーザーチャットのページネーション機能を提供するフック
 * @param fetchUserChatsAndChannelsPaginated ページネーション対応の取得関数
 * @param pageSize 1ページあたりの件数（デフォルト: 20）
 */
const useUserChatsPagination = (
  fetchUserChatsAndChannelsPaginated?: (
    pageToken?: string,
    pageSize?: number
  ) => Promise<IPaginatedResult<IUserChatItem>>,
  pageSize = 20,
): UseUserChatsPaginationReturnType => {
  const [chatItems, setChatItems] = React.useState<IUserChatItem[]>([]);
  const [hasMore, setHasMore] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [nextPageToken, setNextPageToken] = React.useState<string | undefined>();

  // データをリセットする関数
  const reset = React.useCallback(() => {
    setChatItems([]);
    setHasMore(false);
    setIsLoading(false);
    setError(null);
    setNextPageToken(undefined);
  }, []);

  // 初回データを読み込む関数
  const loadInitial = React.useCallback(async () => {
    if (!fetchUserChatsAndChannelsPaginated) {
      setError('取得関数が利用できません');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchUserChatsAndChannelsPaginated(undefined, pageSize);
      setChatItems(result.items);
      setHasMore(result.hasMore);
      setNextPageToken(result.nextPageToken);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '不明なエラーが発生しました';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchUserChatsAndChannelsPaginated, pageSize]);

  // 次のページを読み込む関数
  const loadMore = React.useCallback(async () => {
    if (!fetchUserChatsAndChannelsPaginated || !hasMore || !nextPageToken || isLoading) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchUserChatsAndChannelsPaginated(nextPageToken, pageSize);
      setChatItems(prev => [...prev, ...result.items]);
      setHasMore(result.hasMore);
      setNextPageToken(result.nextPageToken);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '不明なエラーが発生しました';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchUserChatsAndChannelsPaginated, hasMore, nextPageToken, isLoading, pageSize]);

  return {
    chatItems,
    hasMore,
    isLoading,
    error,
    loadMore,
    reset,
    loadInitial,
  };
};

export default useUserChatsPagination;
