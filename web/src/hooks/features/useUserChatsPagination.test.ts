import { renderHook, act } from '@testing-library/react-hooks';
import useUserChatsPagination from './useUserChatsPagination';
import { IUserChatItem, IPaginatedResult } from '../accessors/useUserChatsAndChannelsAccessor';

describe('useUserChatsPagination', () => {
  // モックデータ
  const mockFirstPageItems: IUserChatItem[] = [
    {
      id: 'chat1',
      name: 'テストチャット1',
      type: 'チャット',
      chatType: 'oneOnOne',
    },
    {
      id: 'chat2',
      name: 'テストチャット2',
      type: 'チャット',
      chatType: 'group',
    },
  ];

  const mockSecondPageItems: IUserChatItem[] = [
    {
      id: 'chat3',
      name: 'テストチャット3',
      type: 'チャット',
      chatType: 'oneOnOne',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('初期化', () => {
    it('初期状態が正しく設定される', () => {
      const mockFetch = jest.fn();
      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      expect(result.current.chatItems).toEqual([]);
      expect(result.current.hasMore).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('取得関数が未定義の場合でも正常に動作する', () => {
      const { result } = renderHook(() => useUserChatsPagination(undefined));

      expect(result.current.chatItems).toEqual([]);
      expect(result.current.hasMore).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });

  describe('初回データ読み込み', () => {
    it('初回データを正しく読み込める', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        items: mockFirstPageItems,
        hasMore: true,
        nextPageToken: 'next-token-1',
      } as IPaginatedResult<IUserChatItem>);

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      await act(async () => {
        await result.current.loadInitial();
      });

      expect(result.current.chatItems).toEqual(mockFirstPageItems);
      expect(result.current.hasMore).toBe(true);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(mockFetch).toHaveBeenCalledWith(undefined, 20);
    });

    it('カスタムページサイズで初回データを読み込める', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        items: mockFirstPageItems,
        hasMore: false,
        nextPageToken: undefined,
      } as IPaginatedResult<IUserChatItem>);

      const { result } = renderHook(() => useUserChatsPagination(mockFetch, 5));

      await act(async () => {
        await result.current.loadInitial();
      });

      expect(mockFetch).toHaveBeenCalledWith(undefined, 5);
    });

    it('取得関数が未定義の場合はエラーを設定する', async () => {
      const { result } = renderHook(() => useUserChatsPagination(undefined));

      await act(async () => {
        await result.current.loadInitial();
      });

      expect(result.current.error).toBe('取得関数が利用できません');
      expect(result.current.isLoading).toBe(false);
    });

    it('初回データ読み込み中にエラーが発生した場合の処理', async () => {
      const mockFetch = jest.fn().mockRejectedValue(new Error('ネットワークエラー'));

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      await act(async () => {
        await result.current.loadInitial();
      });

      expect(result.current.error).toBe('ネットワークエラー');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.chatItems).toEqual([]);
    });
  });

  describe('追加データ読み込み', () => {
    it('次のページを正しく読み込める', async () => {
      const mockFetch = jest.fn()
        .mockResolvedValueOnce({
          items: mockFirstPageItems,
          hasMore: true,
          nextPageToken: 'next-token-1',
        } as IPaginatedResult<IUserChatItem>)
        .mockResolvedValueOnce({
          items: mockSecondPageItems,
          hasMore: false,
          nextPageToken: undefined,
        } as IPaginatedResult<IUserChatItem>);

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      // 初回データを読み込み
      await act(async () => {
        await result.current.loadInitial();
      });

      // 次のページを読み込み
      await act(async () => {
        await result.current.loadMore();
      });

      expect(result.current.chatItems).toEqual([...mockFirstPageItems, ...mockSecondPageItems]);
      expect(result.current.hasMore).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(mockFetch).toHaveBeenNthCalledWith(2, 'next-token-1', 20);
    });

    it('hasMoreがfalseの場合は追加読み込みしない', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        items: mockFirstPageItems,
        hasMore: false,
        nextPageToken: undefined,
      } as IPaginatedResult<IUserChatItem>);

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      // 初回データを読み込み
      await act(async () => {
        await result.current.loadInitial();
      });

      // 次のページを読み込み（実際には呼ばれない）
      await act(async () => {
        await result.current.loadMore();
      });

      expect(mockFetch).toHaveBeenCalledTimes(1); // 初回のみ
    });

    it('ローディング中は追加読み込みしない', async () => {
      const mockFetch = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          items: mockFirstPageItems,
          hasMore: true,
          nextPageToken: 'next-token-1',
        }), 100))
      );

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      // 初回データを読み込み開始
      act(() => {
        result.current.loadInitial();
      });

      // ローディング中に追加読み込みを試行
      await act(async () => {
        await result.current.loadMore();
      });

      expect(mockFetch).toHaveBeenCalledTimes(1); // 初回のみ
    });
  });

  describe('リセット機能', () => {
    it('データを正しくリセットできる', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        items: mockFirstPageItems,
        hasMore: true,
        nextPageToken: 'next-token-1',
      } as IPaginatedResult<IUserChatItem>);

      const { result } = renderHook(() => useUserChatsPagination(mockFetch));

      // データを読み込み
      await act(async () => {
        await result.current.loadInitial();
      });

      // リセット
      act(() => {
        result.current.reset();
      });

      expect(result.current.chatItems).toEqual([]);
      expect(result.current.hasMore).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });
});
