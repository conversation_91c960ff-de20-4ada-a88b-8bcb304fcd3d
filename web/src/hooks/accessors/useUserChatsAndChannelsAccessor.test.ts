import { renderHook, act } from '@testing-library/react-hooks';
import useUserChatsAndChannelsAccessor, {
  IUserChatItem,
  IPaginatedResult,
} from './useUserChatsAndChannelsAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { initGraphClient } from './useGraphApiAccessor';

// Graph 初期化をモック
jest.mock('./useGraphApiAccessor', () => ({
  UseGraphApiError: {
    TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
  },
  initGraphClient: jest.fn(),
}));

const mockInitGraphClient = initGraphClient as unknown as jest.Mock;

describe('useUserChatsAndChannelsAccessor', () => {
  const mockTokenProvider: WeakTokenProvider = jest.fn().mockResolvedValue('mock-token');

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('1対1チャット取得機能', () => {
    // fetchUserOneOnOneChatsImpl用のテスト - 1対1チャットのみを取得することを確認
    it('1対1チャットのみを取得して適切な形式で返す', async () => {
      const mockOneOnOneChatsResponse = {
        value: [
          {
            id: 'chat1',
            topic: '田中さんとの1対1チャット',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
          {
            id: 'chat2',
            topic: null,
            chatType: 'oneOnOne',
            members: [
              { displayName: '山田次郎', id: 'user3' },
              { displayName: '鈴木三郎', id: 'user4' },
            ],
          },
        ],
      };

      // Graph クライアントチェーンをモック
      const mockClient = {
        api: jest.fn().mockReturnThis(),
        filter: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockOneOnOneChatsResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let items: IUserChatItem[] = [];
      await act(async () => {
        if (result.current.fetchUserChatsAndChannels) {
          items = await result.current.fetchUserChatsAndChannels();
        }
      });

      // 1対1チャットのみ2件取得されることを確認
      expect(items).toHaveLength(2);

      // 1件目：トピックありの1対1チャット
      expect(items[0]).toEqual({
        id: 'chat1',
        name: '田中さんとの1対1チャット',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 2件目：トピックなしの1対1チャット（メンバー名から生成）
      expect(items[1]).toEqual({
        id: 'chat2',
        name: '山田次郎, 鈴木三郎',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 正しいAPIエンドポイントとフィルターが呼ばれていることを確認
      expect(mockClient.api).toHaveBeenCalledWith('/me/chats');
      // expect(mockClient.filter).toHaveBeenCalledWith("chatType eq 'oneOnOne'");
      expect(mockClient.expand).toHaveBeenCalledWith('members');
      expect(mockClient.orderby).toHaveBeenCalledWith('lastMessagePreview/createdDateTime desc');
    });
  });

  describe('ページネーション機能テスト', () => {
    // ページネーション処理テスト - @odata.nextLinkがある場合の複数ページ取得
    it('@odata.nextLinkがある場合、全ページのデータを取得する', async () => {
      const mockFirstPageResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
          {
            id: 'chat2',
            topic: 'テストチャット2',
            chatType: 'oneOnOne',
            members: [
              { displayName: '山田次郎', id: 'user3' },
              { displayName: '鈴木三郎', id: 'user4' },
            ],
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/chats?$skip=2',
      };

      const mockSecondPageResponse = {
        value: [
          {
            id: 'chat3',
            topic: 'テストチャット3',
            chatType: 'oneOnOne',
            members: [
              { displayName: '高橋五郎', id: 'user5' },
              { displayName: '渡辺六郎', id: 'user6' },
            ],
          },
        ],
        // 最後のページなので@odata.nextLinkなし
      };

      // Graph クライアントチェーンをモック
      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockFirstPageResponse) // 初回リクエスト
          .mockResolvedValueOnce(mockSecondPageResponse), // 2ページ目のリクエスト
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let items: IUserChatItem[] = [];
      await act(async () => {
        if (result.current.fetchUserChatsAndChannels) {
          items = await result.current.fetchUserChatsAndChannels();
        }
      });

      // 全ページのデータが統合されて3件取得されることを確認
      expect(items).toHaveLength(3);

      // 1ページ目のデータ
      expect(items[0]).toEqual({
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット',
        chatType: 'oneOnOne',
      });
      expect(items[1]).toEqual({
        id: 'chat2',
        name: 'テストチャット2',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 2ページ目のデータ
      expect(items[2]).toEqual({
        id: 'chat3',
        name: 'テストチャット3',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 初回リクエストが正しく呼ばれていることを確認
      expect(mockClient.api).toHaveBeenCalledWith('/me/chats');
      expect(mockClient.expand).toHaveBeenCalledWith('members');
      expect(mockClient.orderby).toHaveBeenCalledWith('lastMessagePreview/createdDateTime desc');

      // 2ページ目のリクエストが正しく呼ばれていることを確認
      expect(mockClient.api).toHaveBeenCalledWith('https://graph.microsoft.com/v1.0/me/chats?$skip=2');

      // 合計2回のAPIコールが行われることを確認
      expect(mockClient.get).toHaveBeenCalledTimes(2);
    });

    // ページネーション処理テスト - @odata.nextLinkがない場合の単一ページ取得
    it('@odata.nextLinkがない場合、1ページのみのデータを取得する', async () => {
      const mockSinglePageResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
        ],
        // @odata.nextLinkなし
      };

      // Graph クライアントチェーンをモック
      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockSinglePageResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let items: IUserChatItem[] = [];
      await act(async () => {
        if (result.current.fetchUserChatsAndChannels) {
          items = await result.current.fetchUserChatsAndChannels();
        }
      });

      // 1件のデータが取得されることを確認
      expect(items).toHaveLength(1);
      expect(items[0]).toEqual({
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 1回のAPIコールのみが行われることを確認
      expect(mockClient.get).toHaveBeenCalledTimes(1);
    });

    // ページネーション制限テスト - 最大ページ数制限の確認
    it('最大ページ数制限（100ページ）に達した場合、処理を停止する', async () => {
      // 常に@odata.nextLinkを返すレスポンスを作成（無限ループをシミュレート）
      const mockInfinitePageResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/chats?$skip=1',
      };

      // Graph クライアントチェーンをモック
      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockInfinitePageResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let items: IUserChatItem[] = [];
      await act(async () => {
        if (result.current.fetchUserChatsAndChannels) {
          items = await result.current.fetchUserChatsAndChannels();
        }
      });

      // 最大ページ数制限により、101件のデータが取得されることを確認（初回 + 100ページ）
      expect(items).toHaveLength(101); // 初回ページ + 100ページ × 1件/ページ = 101件

      // 最大101回のAPIコールが行われることを確認（初回 + 100ページ）
      expect(mockClient.get).toHaveBeenCalledTimes(101);
    });
  });

  describe('ページネーション機能のテスト', () => {
    it('初回ページを正しく取得できる', async () => {
      // 初回ページのモックレスポンス
      const mockFirstPageResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [{ displayName: '田中太郎', id: 'user1' }],
          },
          {
            id: 'chat2',
            topic: 'テストチャット2',
            chatType: 'oneOnOne',
            members: [{ displayName: '山田次郎', id: 'user2' }],
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/chats?$skiptoken=next',
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockFirstPageResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let paginatedResult: IPaginatedResult<IUserChatItem> | undefined;
      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          paginatedResult = await result.current.fetchUserChatsAndChannelsPaginated();
        }
      });

      // 結果の検証
      expect(paginatedResult).toBeDefined();
      expect(paginatedResult!.items).toHaveLength(2);
      expect(paginatedResult!.hasMore).toBe(true);
      expect(paginatedResult!.nextPageToken).toBe('https://graph.microsoft.com/v1.0/me/chats?$skiptoken=next');

      // 1件目のデータ
      expect(paginatedResult!.items[0]).toEqual({
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 2件目のデータ
      expect(paginatedResult!.items[1]).toEqual({
        id: 'chat2',
        name: 'テストチャット2',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // APIが正しく呼ばれることを確認
      expect(mockClient.api).toHaveBeenCalledWith('/me/chats');
      expect(mockClient.expand).toHaveBeenCalledWith('members');
      expect(mockClient.orderby).toHaveBeenCalledWith('lastMessagePreview/createdDateTime desc');
      expect(mockClient.top).toHaveBeenCalledWith(20);
    });

    it('次のページを正しく取得できる', async () => {
      // 次のページのモックレスポンス
      const mockNextPageResponse = {
        value: [
          {
            id: 'chat3',
            topic: 'テストチャット3',
            chatType: 'group',
            members: [{ displayName: '鈴木三郎', id: 'user3' }],
          },
        ],
        '@odata.nextLink': undefined, // 最後のページ
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockNextPageResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      const nextPageToken = 'https://graph.microsoft.com/v1.0/me/chats?$skiptoken=next';
      let paginatedResult: IPaginatedResult<IUserChatItem> | undefined;
      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          paginatedResult = await result.current.fetchUserChatsAndChannelsPaginated(nextPageToken);
        }
      });

      // 結果の検証
      expect(paginatedResult).toBeDefined();
      expect(paginatedResult!.items).toHaveLength(1);
      expect(paginatedResult!.hasMore).toBe(false);
      expect(paginatedResult!.nextPageToken).toBeUndefined();

      // データの検証
      expect(paginatedResult!.items[0]).toEqual({
        id: 'chat3',
        name: 'テストチャット3',
        type: 'チャット',
        chatType: 'group',
      });

      // APIが正しく呼ばれることを確認
      expect(mockClient.api).toHaveBeenCalledWith(nextPageToken);
    });

    it('カスタムページサイズで取得できる', async () => {
      const mockResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [{ displayName: '田中太郎', id: 'user1' }],
          },
        ],
        '@odata.nextLink': undefined,
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn().mockResolvedValue(mockResponse),
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          await result.current.fetchUserChatsAndChannelsPaginated(undefined, 5);
        }
      });

      // カスタムページサイズが正しく設定されることを確認
      expect(mockClient.top).toHaveBeenCalledWith(5);
    });
  });
});

// import { renderHook, act } from '@testing-library/react-hooks';
// import useUserChatsAndChannelsAccessor from './useUserChatsAndChannelsAccessor';
// import { WeakTokenProvider } from '../../types/TokenProvider';
// import { UseGraphApiError, initGraphClient } from './useGraphApiAccessor';

// // Graph 初期化をモック
// jest.mock('./useGraphApiAccessor', () => ({
//   UseGraphApiError: {
//     TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
//   },
//   initGraphClient: jest.fn(),
// }));

// const mockInitGraphClient = initGraphClient as unknown as jest.Mock;

// describe('useUserChatsAndChannelsAccessor', () => {
//   const mockTokenProvider: WeakTokenProvider = jest.fn().mockResolvedValue('mock-token');

//   beforeEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('初期化', () => {
//     it('tokenProvider がある場合は fetch 関数が定義され、初期状態は isLoading=false, error=null', () => {
//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       expect(result.current.fetchUserChatsAndChannels).toBeDefined();
//       expect(result.current.isLoading).toBe(false);
//       expect(result.current.error).toBe(null);
//     });

//     it('tokenProvider が未定義の場合は fetch 関数が未定義', () => {
//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(undefined as any));

//       expect(result.current.fetchUserChatsAndChannels).toBeUndefined();
//       expect(result.current.isLoading).toBe(false);
//       expect(result.current.error).toBe(null);
//     });
//   });

//   describe('データ取得', () => {
//     const mockChatsResponse = {
//       value: [
//         {
//           id: 'chat1',
//           topic: 'テストチャット1',
//           chatType: 'oneOnOne',
//           members: [
//             { displayName: 'ユーザー1', id: 'user1' },
//             { displayName: 'ユーザー2', id: 'user2' },
//           ],
//         },
//         {
//           id: 'chat2',
//           topic: null,
//           chatType: 'group',
//           members: [
//             { displayName: 'ユーザー3', id: 'user3' },
//             { displayName: 'ユーザー4', id: 'user4' },
//           ],
//         },
//       ],
//     };

//     const mockTeamsResponse = {
//       value: [
//         { id: 'team1', displayName: 'テストチーム1' },
//       ],
//     };

//     const mockChannelsResponse = {
//       value: [
//         { id: 'channel1', displayName: 'テストチャネル1', membershipType: 'standard' },
//         { id: 'channel2', displayName: 'テストチャネル2', membershipType: 'private' },
//       ],
//     };

//     it('チャットとチャネルを統合して返す（トピックなしはメンバー名連結）', async () => {
//       // Graph クライアントチェーンをモック
//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         orderby: jest.fn().mockReturnThis(),
//         get: jest.fn()
//           // /me/chats
//           .mockResolvedValueOnce(mockChatsResponse)
//           // /me/joinedTeams
//           .mockResolvedValueOnce(mockTeamsResponse)
//           // /teams/team1/channels
//           .mockResolvedValueOnce(mockChannelsResponse),
//       };

//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       let items: any[] = [];
//       await act(async () => {
//         items = await result.current.fetchUserChatsAndChannels!();
//       });

//       // 2チャット + 2チャネル
//       expect(items).toHaveLength(4);

//       // チャット
//       expect(items[0]).toEqual({
//         id: 'chat1',
//         name: 'テストチャット1',
//         type: 'チャット',
//         chatType: 'oneOnOne',
//       });
//       // topic が null のため members から名称を生成
//       expect(items[1]).toEqual({
//         id: 'chat2',
//         name: 'ユーザー3, ユーザー4',
//         type: 'チャット',
//         chatType: 'group',
//       });

//       // チャネル（teamId を含む・chatType は TeamsChannel 固定）
//       expect(items[2]).toEqual({
//         id: 'channel1',
//         name: 'テストチーム1 - テストチャネル1',
//         type: 'チャネル',
//         chatType: 'TeamsChannel',
//         teamId: 'team1',
//       });
//       expect(items[3]).toEqual({
//         id: 'channel2',
//         name: 'テストチーム1 - テストチャネル2',
//         type: 'チャネル',
//         chatType: 'TeamsChannel',
//         teamId: 'team1',
//       });

//       // initGraphClient が呼ばれていること
//       expect(mockInitGraphClient).toHaveBeenCalledWith(mockTokenProvider);
//     });

//     it('initGraphClient が null を返す場合、例外を投げて error state を設定する', async () => {
//       mockInitGraphClient.mockReturnValue(null);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       await act(async () => {
//         await expect(result.current.fetchUserChatsAndChannels!()).rejects.toEqual(
//           new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE),
//         );
//       });

//       expect(result.current.error).toBe(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE);
//     });

//     it('API 側で例外が起きた場合、error state を設定して再throw する', async () => {
//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         orderby: jest.fn().mockReturnThis(),
//         get: jest.fn().mockRejectedValue(new Error('API Error')),
//       };
//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       await act(async () => {
//         await expect(result.current.fetchUserChatsAndChannels!()).rejects.toEqual(
//           new Error('API Error'),
//         );
//       });

//       expect(result.current.error).toBe('API Error');
//       expect(result.current.isLoading).toBe(false);
//     });
//   });

//   describe('ローディング状態', () => {
//     it('取得開始直後は isLoading が true', () => {
//       const pending = new Promise<never>(() => {
//         // resolve/reject しない：常に pending
//       });

//       const mockClient = {
//         api: jest.fn().mockReturnThis(),
//         expand: jest.fn().mockReturnThis(),
//         orderby: jest.fn().mockReturnThis(),
//         get: jest.fn().mockImplementation(() => pending),
//       };
//       mockInitGraphClient.mockReturnValue(mockClient);

//       const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

//       act(() => {
//         result.current.fetchUserChatsAndChannels!(); // awaitしない
//       });

//       expect(result.current.isLoading).toBe(true);
//     });
//   });
// });
