import * as React from 'react';
import {
  <PERSON><PERSON>, <PERSON>er, Input, Loader, Tooltip,
} from '@fluentui/react-northstar';
import {
  AddIcon, AcceptIcon, CloseIcon, InfoIcon,
} from '@fluentui/react-icons-northstar';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
// import TeamsSettingTabs, { TeamsSettingTabType, TeamsSettingTabTypeValue }
//   from './TeamsSettingTabs';
import { TeamsSettingTabType, TeamsSettingTabTypeValue } from './TeamsSettingTabs';
import SelectedItemsList from './SelectedItemsList';
import { UseTeamsChatsApiReturnType } from '../../../../hooks/accessors/useTeamsChatsApiAccessor';
import MessageToaster, { ToasterMessage } from '../../../commons/molecules/message-toaster/MessageToaster';
import useMessageToasterBehavior from '../../../../hooks/behaviors/useMessageToasterBehavior';
import useTeamsSettingData from '../../../../hooks/features/useTeamsSettingData';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';

// CSS
import './TeamsSettingModal.scss';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType,
  eventReporter: EventReporter;
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
    eventReporter,
  } = props;

  const {
    // 新規登録、更新
    postTeamsChatsApi,
    // 同期
    getTeamsChatsApi,
    // 削除
    deleteTeamsChatsApi,
  } = useTeamsChatsApiAccessorReturn;

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
  };

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    // fetchUserChatsAndChannels,
    fetchUserChatsAndChannelsPaginated,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // IndexedDB features
  const [openDB] = useIndexedDbAccessor();

  // ページネーション状態管理
  const [hasMoreChats, setHasMoreChats] = React.useState(false);
  // 各ページのトークンを保存
  const [pageTokens, setPageTokens] = React.useState<(string | undefined)[]>([undefined]);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [isLoadingPage, setIsLoadingPage] = React.useState(false);
  const [pageData, setPageData] = React.useState<{ [page: number]: IUserChatItem[] }>({});

  // TeamsSettingDataフックを使用（ページネーション機能なし版）
  const {
    savedItems,
    isLoadingSavedItems,
    saveSelectedItems,
    setSavedItems,
    loadSavedItems,
  } = useTeamsSettingData({
    fetchUserChatsAndChannels: undefined, // ページネーション版を使用するため無効化
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen: open || false,
    openDB,
    eventReporter,
  });

  // 指定されたページのデータを取得する関数
  const loadPageData = React.useCallback(async (page: number) => {
    if (!fetchUserChatsAndChannelsPaginated) return;

    // 既にそのページのデータがある場合はスキップ
    if (pageData[page]) {
      return;
    }

    setIsLoadingPage(true);
    try {
      const pageToken = pageTokens[page - 1]; // ページ番号は1から始まるが、配列は0から
      const result = await fetchUserChatsAndChannelsPaginated(pageToken);

      // ページデータを保存
      const newPageData = { ...pageData, [page]: result.items };
      setPageData(newPageData);

      // 次のページのトークンを保存
      if (result.hasMore && result.nextPageToken) {
        setPageTokens((prev) => {
          const newTokens = [...prev];
          if (newTokens.length === page) {
            newTokens.push(result.nextPageToken);
          }
          return newTokens;
        });
      }

      setHasMoreChats(result.hasMore);
    } catch (err) {
      throw new Error(`ページ${page}のデータ取得エラー: ${err}`);
    } finally {
      setIsLoadingPage(false);
    }
  }, [fetchUserChatsAndChannelsPaginated, pageData, pageTokens]);

  // ページ変更ハンドラー
  const handlePageChange = React.useCallback((page: number) => {
    setCurrentPage(page);
    loadPageData(page);
  }, [loadPageData]);

  // データ取得のEffect
  React.useEffect(() => {
    if ((open || false) && fetchUserChatsAndChannelsPaginated) {
      // 並行処理
      Promise.all([
        loadPageData(1), // 初回は1ページ目を取得
        loadSavedItems(),
      ]).catch((err) => {
        throw new Error(`データ取得エラー: ${err}`);
      });
    }
  }, [open, fetchUserChatsAndChannelsPaginated, loadPageData, loadSavedItems]);

  // 検索状態管理
  const [searchQuery, setSearchQuery] = React.useState('');
  // 検索後アイテム
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  // 選択されたアイテム
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  // タブ状態管理
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );

  // 保存状態管理
  const [isSaving, setIsSaving] = React.useState(false);
  // 保存完了状態管理
  const [isSaveCompleted, setIsSaveCompleted] = React.useState(false);
  // トースター機能
  const [isToasterShown, toasterMessage, extendPopupTimer] = useMessageToasterBehavior(3000);
  // 選択上限数
  const MAX_SELECTION_COUNT = 10;
  // 統合されたローディング状態
  const isLoadingData = isLoading || isLoadingSavedItems || isLoadingPage;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    const hasSelectedItems = selectedItems.size > 0 ? 'has-selected-items' : '';
    return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
  }, [className, open, selectedItems.size]);

  // 保存済みアイテムを選択状態に反映するEffect
  React.useEffect(() => {
    setSelectedItems(new Set(savedItems));
  }, [savedItems]);

  // 検索用の全データを取得
  const allLoadedItems = React.useMemo(() => Object.values(pageData).flat(), [pageData]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allLoadedItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
    // 検索結果が変わったら1ページ目に戻る
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [searchQuery, allLoadedItems, activeTab, currentPage]);

  // 現在のページに表示するアイテムを計算
  const ITEMS_PER_PAGE = 20;
  const currentPageItems = React.useMemo(() => {
    if (searchQuery) {
      // 検索時は検索結果をページネーション
      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      return filteredChatItems.slice(startIndex, endIndex);
    }
    // 通常時は現在のページのデータのみを表示（各ページ独立）
    return pageData[currentPage] || [];
  }, [searchQuery, filteredChatItems, currentPage, pageData]);

  // 総ページ数を計算
  const totalPages = React.useMemo(() => {
    if (searchQuery) {
      // 検索時は検索結果の総ページ数
      return Math.ceil(filteredChatItems.length / ITEMS_PER_PAGE);
    }
    // 通常時は現在のページ + (次のページがあるかどうか)
    return hasMoreChats ? currentPage + 1 : currentPage;
  }, [searchQuery, filteredChatItems.length, hasMoreChats, currentPage]);

  const handleClose = React.useCallback(() => {
    // 状態をリセット
    setSearchQuery('');
    setSelectedItems(new Set());
    setSavedItems(new Set());
    setActiveTab(TeamsSettingTabType.CHAT);
    setIsSaving(false);
    setIsSaveCompleted(false);
    // ページネーション状態もリセット
    setHasMoreChats(false);
    setPageTokens([undefined]);
    setCurrentPage(1);
    setPageData({});

    if (onClose) onClose();
  }, [onClose, setSavedItems]);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // タブ変更ハンドラー
  // const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
  //   setActiveTab(tab);
  //   // タブ切り替え時に検索クエリをクリア
  //   setSearchQuery('');
  // }, []);

  // チャットとチャネルの件数を計算
  // const chatCount = React.useMemo(() => allChatItems.filter((item) => item.type
  // === TeamsSettingTabType.CHAT).length, [allChatItems]);

  // const channelCount = React.useMemo(() => allChatItems.filter((item) => item.type
  // === TeamsSettingTabType.CHANNEL).length, [allChatItems]);

  // プレースホルダーテキストをタブに応じて変更
  // const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT
  //   ? 'チャット名で検索' : 'チャネル名で検索'), [activeTab]);
  const searchPlaceholder = 'チャット名で検索';

  // 保存ボタンのコンテンツ
  const saveButtonContent = React.useMemo(() => {
    if (isSaving) {
      return (
        <>
          <Loader size="smallest" inline />
          {' '}
          保存中...
        </>
      );
    }
    if (isSaveCompleted) {
      return '保存完了';
    }
    return '保存';
  }, [isSaving, isSaveCompleted]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        // 選択解除の場合
        newSet.delete(id);
      } else {
        // 選択追加の場合：上限チェック
        if (newSet.size >= MAX_SELECTION_COUNT) {
          // 上限に達している場合はトースターメッセージを表示
          extendPopupTimer(ToasterMessage.MAX_TEAMS_SELECTION);
          return prev; // 状態を変更しない
        }
        newSet.add(id);
      }
      return newSet;
    });
  }, [extendPopupTimer]);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(id);
    }
  }, [handleItemToggle]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    if (!saveSelectedItems) {
      throw new Error('保存機能が利用できません');
    }
    // ローディング状態を開始
    setIsSaving(true);
    try {
      // キュー処理を使用して保存
      await saveSelectedItems(selectedItems, allLoadedItems);
      // 保存完了状態を表示
      setIsSaving(false);
      setIsSaveCompleted(true);
      // 0.8秒後にモーダルを閉じる
      setTimeout(() => {
        handleClose();
        // 閉じる時間を管理
      }, 800);
    } finally {
      // ローディング状態を終了（保存完了時は上で既に設定済み）
      if (!isSaveCompleted) {
        setIsSaving(false);
      }
    }
  }, [
    saveSelectedItems,
    selectedItems,
    allLoadedItems,
    handleClose,
    isSaveCompleted]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={TeamsSettingLabel.TITLE} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <div className="simple-modal-description">
              <p>検索対象を選択できます。最大10件まで選択可能です。</p>
              <Tooltip
                content="チャット選択後、検索可能になるまで最大1時間程度お待ちください。"
                trigger={(
                  <Button
                    text
                    iconOnly
                    icon={<InfoIcon />}
                    className="teams-setting-info-button"
                    aria-label="データ取得に関する情報"
                  />
                )}
              />
            </div>
            {/* タブ切り替え */}
            {/* <TeamsSettingTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              disabled={isLoadingData}
              chatCount={chatCount}
              channelCount={channelCount}
            /> */}
            {/* 検索フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fluid
              />
            </div>
            {/* 選択されたアイテム一覧 */}
            <SelectedItemsList
              selectedItems={selectedItems}
              allChatItems={allLoadedItems}
              onRemoveItem={handleRemoveSelectedItem}
            />
            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoadingData && (
                <div className="simple-modal-loading">
                  <p>チャットを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoadingData && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoadingData && !error && currentPageItems.map((item: IUserChatItem) => {
                const isSelected = selectedItems.has(item.id);
                const itemClassName = `simple-modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item.id)}
                    onKeyDown={(event) => handleKeyDown(event, item.id)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="simple-modal-chat-item-content">
                      <span className="simple-modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
            {/* ページネーション */}
            {!isLoadingData && !error && totalPages > 1 && (
              <div className="simple-modal-pagination">
                <div className="pagination-info">
                  <span>
                    {searchQuery ? (
                      <>
                        {filteredChatItems.length}
                        件中
                        {((currentPage - 1) * ITEMS_PER_PAGE) + 1}
                        -
                        {Math.min(currentPage * ITEMS_PER_PAGE, filteredChatItems.length)}
                        件を表示
                      </>
                    ) : null}
                  </span>
                </div>
                <div className="pagination-controls">
                  <Button
                    content="前へ"
                    disabled={currentPage === 1 || isLoadingPage}
                    onClick={() => handlePageChange(currentPage - 1)}
                    size="small"
                  />
                  <span className="pagination-current">
                    {currentPage}
                    {' '}
                    /
                    {totalPages}
                  </span>
                  <Button
                    content="次へ"
                    disabled={currentPage === totalPages || isLoadingPage}
                    onClick={() => handlePageChange(currentPage + 1)}
                    size="small"
                  />
                </div>
              </div>
            )}
            {/* 保存ボタン */}
            <div className="simple-modal-save-section" style={{ marginTop: '20px', padding: '0 20px' }}>
              <Button
                primary={!isSaveCompleted}
                content={saveButtonContent}
                disabled={
                  !postTeamsChatsApi
                  || !deleteTeamsChatsApi
                  || isSaving
                  || isSaveCompleted
                  || (selectedItems.size === 0 && savedItems.size === 0)
                  || (selectedItems.size === savedItems.size
                      && Array.from(selectedItems).every((id) => savedItems.has(id)))
                }
                onClick={handleSave}
                fluid
                styles={isSaveCompleted ? {
                  root: {
                    backgroundColor: '#107c10',
                    borderColor: '#107c10',
                    color: 'white',
                  },
                } : undefined}
              />
            </div>
          </div>
        </div>
      </div>
      {/* トースターメッセージ */}
      <MessageToaster
        isActive={isToasterShown}
        messageType={toasterMessage}
      />
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  open: false,
};

export default TeamsSettingModal;
